import { useRouter } from "expo-router";
import { onAuthStateChanged, signOut } from "firebase/auth";
import { addDoc, collection, deleteDoc, doc, getDocs, onSnapshot, query, updateDoc, where } from "firebase/firestore";
import { useEffect, useState } from "react";
import { ActivityIndicator, Alert, Button, FlatList, Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { auth, db } from "../../config/firebase";
import AddObatScreen from "./components/AddObatScreen"; // Contains AddObatScreen component
import EditObatScreen from "./components/EditObatScreen"; // Contains EditObatScreen component
import ObatItem from "./components/ObatItem"; // Contains ObatItem component

export default function DetailObatScreen() {
  const [user, setUser] = useState(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const [obat, setObat] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editMedication, setEditMedication] = useState(null);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  // State untuk konfirmasi hapus
  const [konfirmasiVisible, setKonfirmasiVisible] = useState(false);
  const [selectedObat, setSelectedObat] = useState(null);
  const router = useRouter();

  // Cek user login
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (!currentUser) {
        router.replace("/Login");
      } else {
        setUser(currentUser);
        console.log("User ID saat ini:", currentUser.uid);
      }
      setLoadingUser(false);
    });

    return unsubscribe;
  }, []);

  // Ambil data obat dari Firestore
  useEffect(() => {
    if (!user) return;

    setError(null);

    try {
      console.log("🔍 Memulai fetch obat untuk user:", user.uid);
      console.log("📁 Using collection: 'obat'");

      // Gunakan collection obat (sesuai dokumentasi)
      const obatRef = collection(db, "obat");
      const q = query(obatRef, where("userId", "==", user.uid));
      // const t = query(obatrep, where("obatId", "==", obat.uid));

      const unsubscribe = onSnapshot(q,
        (snapshot) => {
          console.log("📊 Jumlah dokumen obat dari 'obat':", snapshot.docs.length);
          console.log("📋 Raw snapshot docs:", snapshot.docs.map(doc => ({id: doc.id, data: doc.data()})));

          const list = snapshot.docs.map((doc) => {
            console.log("📄 Obat item:", doc.id, doc.data());
            return { id: doc.id, ...doc.data() };
          });
          // Urutkan berdasarkan tanggal dibuat (terbaru dulu) dengan handling yang lebih robust
          list.sort((a, b) => {
            let dateA, dateB;

            // Handle Firebase Timestamp
            if (a.createdAt?.toDate) {
              dateA = a.createdAt.toDate();
            } else if (a.createdAt) {
              dateA = new Date(a.createdAt);
            } else {
              dateA = new Date(0); // Default ke epoch jika tidak ada tanggal
            }

            if (b.createdAt?.toDate) {
              dateB = b.createdAt.toDate();
            } else if (b.createdAt) {
              dateB = new Date(b.createdAt);
            } else {
              dateB = new Date(0);
            }

            return dateB - dateA; // Terbaru dulu
          });
          setObat(list);
        },
        (error) => {
          console.error("Error onSnapshot obat:", error);

          // Jika error karena index requirement, coba dengan getDocs sebagai fallback
          if (error.code === 'failed-precondition' || error.message.includes('index')) {
            console.log("🔄 Index error detected, mencoba fallback ke getDocs...");

            getDocs(q).then((snapshot) => {
              console.log("✅ Fallback getDocs berhasil, jumlah dokumen:", snapshot.docs.length);
              const list = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

              // Sorting yang lebih robust
              list.sort((a, b) => {
                let dateA, dateB;

                // Handle Firebase Timestamp
                if (a.createdAt?.toDate) {
                  dateA = a.createdAt.toDate();
                } else if (a.createdAt) {
                  dateA = new Date(a.createdAt);
                } else {
                  dateA = new Date(0); // Default ke epoch jika tidak ada tanggal
                }

                if (b.createdAt?.toDate) {
                  dateB = b.createdAt.toDate();
                } else if (b.createdAt) {
                  dateB = new Date(b.createdAt);
                } else {
                  dateB = new Date(0);
                }

                return dateB - dateA; // Terbaru dulu
              });

              setObat(list);
              setError(null); // Clear error jika fallback berhasil
              console.log("✅ Data obat berhasil dimuat dengan fallback");
            }).catch(err => {
              console.error("❌ Fallback error:", err);
              setError("Gagal memuat data obat. Silakan coba lagi atau periksa koneksi internet.");
            });
          } else {
            // Error lain selain index
            setError(`Error: ${error.message}`);
            console.error("❌ Non-index error:", error);
          }
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error("Error setting up obat listener:", error);
      setError(error.message);
    }
  }, [user]);

  // Fungsi untuk toggle status aktif/tidak aktif obat
  const toggleMedicationActive = async (id, currentStatus) => {
    try {
      await updateDoc(doc(db, "obat", id), {
        isActive: !currentStatus,
        updatedAt: new Date()
      });
      console.log("Status obat berhasil diupdate");
    } catch (error) {
      console.error("Error toggle obat status:", error);
      Alert.alert("Error", "Gagal mengubah status obat");
    }
  };

  // Mencari obat dan menampilkan modal konfirmasi

  const deleteObat = (id) => {
    const obatDihapus = obat.find(item => item.id === id);
    if (!obatDihapus) {
      Alert.alert("Error", "Data obat tidak ditemukan");
      return;
    }
    setSelectedObat(obatDihapus);
    setKonfirmasiVisible(true);
  };

  //  Menghapus dari Firestore dan menutup modal

  const handleKonfirmasiHapus = async () => {
    try {
      await deleteDoc(doc(db, "obat", selectedObat.id));
      setKonfirmasiVisible(false);
      setSelectedObat(null);
      Alert.alert("Sukses", "Obat berhasil dihapus!");
    } catch (error) {
      console.error("Error deleting obat:", error);
      Alert.alert("Error", "Gagal menghapus obat: " + error.message);
    }
  };

  // Fungsi untuk membuka modal edit
  const openEditModal = (medication) => {
    setEditMedication(medication);
    setEditModalVisible(true);
  };

  // Fungsi untuk menyimpan perubahan edit obat
  const handleEditSave = async (updatedMedicationData) => {
    if (!editMedication) {
      Alert.alert("Error", "Data obat tidak ditemukan");
      return;
    }

    setLoading(true);
    try {
      await updateDoc(doc(db, "obat", editMedication.id), updatedMedicationData);
      console.log("Obat berhasil diedit");
      Alert.alert("Sukses", "Obat berhasil diperbarui");
      setEditModalVisible(false);
      setEditMedication(null);
    } catch (error) {
      console.error("Error edit obat:", error);
      Alert.alert("Error", "Gagal mengedit obat: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk membatalkan edit
  const handleEditCancel = () => {
    setEditModalVisible(false);
    setEditMedication(null);
  };

  // Fungsi untuk menambah obat baru
  const addMedication = async (medicationData) => {
    if (!user) {
      Alert.alert("Error", "User tidak terautentikasi");
      return;
    }

    setLoading(true);
    try {
      console.log("Menambahkan obat baru untuk user:", user.uid);

      const obatRef = collection(db, "obat");
      const newMedication = {
        ...medicationData,
        userId: user.uid,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await addDoc(obatRef, newMedication);
      console.log("Obat berhasil ditambahkan");
      Alert.alert("Sukses", "Obat berhasil ditambahkan");
      setAddModalVisible(false);
    } catch (error) {
      console.error("Error menambahkan obat:", error);
      Alert.alert("Error", "Gagal menambahkan obat: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    console.log("=== LOGOUT INITIATED FROM DETAIL OBAT ===");
    try {
      await signOut(auth);
      console.log("Firebase signOut successful");
      // Redirect ke Login tanpa parameter reset
      router.replace("/Login");
    } catch (error) {
      console.error("Logout error:", error);
      Alert.alert("Error", "Gagal logout: " + error.message);
    }
  };

  // Tampilkan loading selama pengecekan user
  if (loadingUser) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Memuat...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            console.log("< Back button pressed");
            router.replace("/Home");
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Detail Obat</Text>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
          <Text style={styles.errorSubText}>
            {error.includes('index') || error.includes('failed-precondition') ?
              'Firebase membutuhkan index untuk query ini. Klik link di console untuk membuat index secara otomatis.' :
              'Periksa console dan pastikan koneksi Firebase berfungsi dengan baik'
            }
          </Text>
        </View>
      )}

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>Memproses operasi...</Text>
        </View>
      )}

      {obat.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Belum ada obat yang dicatat</Text>
          <Text style={styles.emptySubText}>Kembali ke halaman utama untuk menambah obat</Text>
          <Button
            title="TAMBAH OBAT"
            onPress={() => setAddModalVisible(true)}
            color="#4A90E2"
          />
        </View>
      ) : (
        <FlatList
          data={obat}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ObatItem
              item={item}
              onToggleActive={toggleMedicationActive}
              onDelete={deleteObat}
              onEdit={openEditModal}
            />
          )}
          showsVerticalScrollIndicator={false}
          style={styles.list}
          contentContainerStyle={styles.listContent}
        />
      )}



      <EditObatScreen
        visible={editModalVisible}
        medication={editMedication}
        onSave={handleEditSave}
        onCancel={handleEditCancel}
      />

      <AddObatScreen
        visible={addModalVisible}
        onAddMedication={addMedication}
        onCancel={() => setAddModalVisible(false)}
      />

      {/* Modal Konfirmasi Hapus */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={konfirmasiVisible}
        onRequestClose={() => setKonfirmasiVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Konfirmasi Hapus</Text>
            <Text style={styles.modalMessage}>
              Yakin ingin menghapus obat "{selectedObat?.name}"?
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setKonfirmasiVisible(false);
                  setSelectedObat(null);
                }}
              >
                <Text style={styles.cancelButtonText}>Batal</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.deleteButton]}
                onPress={handleKonfirmasiHapus}
              >
                <Text style={styles.deleteButtonText}>Hapus</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0f0f0",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#4A90E2",
    paddingTop: 25,
    paddingBottom: 10,
    paddingHorizontal: 15,
    justifyContent: "center",
    minHeight: 60,
  },
  backButton: {
    position: "absolute",
    left: 10,
    top: 0,
    bottom: 0,
    zIndex: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    justifyContent: "center",
    alignItems: "center",
    minWidth: 50,
    minHeight: 50,
  },
  backIcon: {
    fontSize: 20,
    color: "#ffffff",
    fontWeight: "bold",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#ffffff",
    textAlign: "center",
    letterSpacing: 0.3,
    marginTop: -9,
  },
  errorContainer: {
    margin: 20,
    padding: 15,
    backgroundColor: "#ffebee",
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#f44336",
  },
  errorText: {
    color: "#c62828",
    fontWeight: "600",
    marginBottom: 5,
  },
  errorSubText: {
    color: "#c62828",
    fontSize: 12,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  loadingText: {
    marginLeft: 10,
    color: "#1976d2",
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    color: "#666",
    textAlign: "center",
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
    marginBottom: 20,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    padding: 20,
    backgroundColor: "#ffffff",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  buttonSpacer: {
    width: 10,
  },
  // Styles untuk modal konfirmasi hapus
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    margin: 20,
    minWidth: 300,
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
    color: '#333',
  },
  modalMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  deleteButton: {
    backgroundColor: '#ff4444',
  },
  cancelButtonText: {
    color: '#333',
    fontWeight: '600',
    fontSize: 16,
  },
  deleteButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});
