# 🔍 Debug Data Issue - Input Data Menghilang

## ❌ **Problem Identified**
Data inputan yang pernah diinput menghilang dari aplikasi.

## 🔧 **Possible Causes & Solutions**

### 1. **Collection Name Mismatch**
**Issue**: Mungkin data tersimpan di collection "obat" tapi kita baca dari "pencatatanobat"

**Debug Steps Added**:
```javascript
// Home.jsx - Now checks both collections
const collections = ["pencatatanobat", "obat"];
for (const collectionName of collections) {
  console.log(`🔍 Checking collection: ${collectionName}`);
  const snapshot = await getDocs(q);
  console.log(`📊 Found ${snapshot.docs.length} documents in ${collectionName}`);
}
```

### 2. **User ID Mismatch**
**Issue**: User ID mungkin berubah atau tidak match

**Debug Added**:
```javascript
console.log("🔍 Fetching medication data for user:", user.uid);
```

### 3. **Firebase Rules**
**Issue**: Firestore rules mungkin block read access

### 4. **Data Structure Change**
**Issue**: Field names atau structure berubah

## 🧪 **Testing Steps**

### Step 1: Check Console Logs
Run aplikasi dan check console untuk:
```
🔍 Fetching medication data for user: [USER_ID]
🔍 Checking collection: pencatatanobat
📊 Found X documents in pencatatanobat
🔍 Checking collection: obat  
📊 Found Y documents in obat
```

### Step 2: Check Firebase Console
1. Buka Firebase Console
2. Go to Firestore Database
3. Check collections:
   - `pencatatanobat`
   - `obat`
4. Verify data exists dengan user ID yang benar

### Step 3: Manual Data Check
```javascript
// Add this temporarily to Home.jsx for testing
useEffect(() => {
  const testFirestore = async () => {
    if (!user) return;
    
    console.log("=== MANUAL FIRESTORE TEST ===");
    console.log("User ID:", user.uid);
    
    // Test collection 1
    const ref1 = collection(db, "pencatatanobat");
    const snap1 = await getDocs(ref1);
    console.log("Total docs in pencatatanobat:", snap1.docs.length);
    
    // Test collection 2  
    const ref2 = collection(db, "obat");
    const snap2 = await getDocs(ref2);
    console.log("Total docs in obat:", snap2.docs.length);
    
    // Test with user filter
    const userQuery = query(ref1, where("userId", "==", user.uid));
    const userSnap = await getDocs(userQuery);
    console.log("User docs in pencatatanobat:", userSnap.docs.length);
  };
  
  testFirestore();
}, [user]);
```

## 🔧 **Quick Fixes to Try**

### Fix 1: Use Original Collection Name
```javascript
// If data was saved in "obat", change back to:
const obatRef = collection(db, "obat");
```

### Fix 2: Check All Collections
```javascript
// Check both collections and use whichever has data
const checkBothCollections = async () => {
  const collections = ["obat", "pencatatanobat", "medications"];
  
  for (const name of collections) {
    const ref = collection(db, name);
    const q = query(ref, where("userId", "==", user.uid));
    const snap = await getDocs(q);
    
    if (snap.docs.length > 0) {
      console.log(`✅ Found data in: ${name}`);
      return { ref, data: snap.docs };
    }
  }
  
  console.log("❌ No data found in any collection");
  return null;
};
```

### Fix 3: Reset and Re-add Data
```javascript
// If data is truly lost, add test data:
const addTestData = async () => {
  const testMedication = {
    name: "Paracetamol",
    dosage: "500mg", 
    frequency: "3x sehari",
    instructions: "setelah makan",
    sideEffects: "",
    userId: user.uid,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  await addDoc(collection(db, "pencatatanobat"), testMedication);
  console.log("✅ Test data added");
};
```

## 🎯 **Expected Console Output**

**If Data Exists**:
```
🔍 Fetching medication data for user: abc123
🔍 Checking collection: pencatatanobat
📊 Found 3 documents in pencatatanobat
📄 Document 1: {name: "Paracetamol", dosage: "500mg", ...}
📄 Document 2: {name: "Amoxicillin", dosage: "250mg", ...}
✅ Latest medication data: {name: "Paracetamol", dosage: "500mg", ...}
```

**If Data Missing**:
```
🔍 Fetching medication data for user: abc123
🔍 Checking collection: pencatatanobat
📊 Found 0 documents in pencatatanobat
🔍 Checking collection: obat
📊 Found 0 documents in obat
❌ No medication data found in any collection
```

## 🚀 **Next Steps**

1. **Run app** dan check console logs
2. **Check Firebase Console** untuk verify data
3. **Try adding new medication** untuk test if saving works
4. **Report console output** untuk further debugging

## 🔄 **Temporary Workaround**

Jika data benar-benar hilang, kita bisa:
1. Add sample data untuk testing
2. Implement data backup/restore
3. Add better error handling
4. Implement offline storage sebagai backup

---

**Status**: 🔍 **DEBUGGING IN PROGRESS** - Added extensive logging to identify root cause
