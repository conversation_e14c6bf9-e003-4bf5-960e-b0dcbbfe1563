import { FlatList, StyleSheet, Text, View } from "react-native";
import ObatItem from "./ObatItem";

export default function ObatList({ medications, onToggleActive, onDelete, onEdit }) {
  if (!medications || medications.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Belum ada obat yang dicatat</Text>
        <Text style={styles.emptySubText}>Tambahkan obat pertama Anda menggunakan form di atas</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.listTitle}>Daftar Obat ({medications.length})</Text>
      <FlatList
        data={medications}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <ObatItem
            item={item}
            onToggleActive={onToggleActive}
            onDelete={onDelete}
            onEdit={onEdit}
          />
        )}
        showsVerticalScrollIndicator={false}
        style={styles.list}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 20,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
    color: "#333",
  },
  list: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 50,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
  },
});