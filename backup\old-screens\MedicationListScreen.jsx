import React, { useEffect, useState } from "react";
import { View, Text, Button, Alert, ActivityIndicator, StyleSheet, FlatList } from "react-native";
import { useRouter } from "expo-router";
import { signOut, onAuthStateChanged } from "firebase/auth";
import { auth, db } from "../../config/firebase";
import { collection, query, where, onSnapshot, deleteDoc, updateDoc, doc, getDocs } from "firebase/firestore";
import MedicationItem from "./components/TodoItem"; // Contains MedicationItem component
import EditMedicationModal from "./components/EditTodoModal"; // Contains EditMedicationModal component

export default function MedicationListScreen() {
  const [user, setUser] = useState(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const [medications, setMedications] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editMedication, setEditMedication] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Cek user login
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (!currentUser) {
        router.replace("/Login");
      } else {
        setUser(currentUser);
        console.log("User ID saat ini:", currentUser.uid);
      }
      setLoadingUser(false);
    });

    return unsubscribe;
  }, []);

  // Ambil data obat dari Firestore
  useEffect(() => {
    if (!user) return;

    setError(null);
    
    try {
      console.log("Memulai fetch medications untuk user:", user.uid);
      
      const medicationsRef = collection(db, "medications");
      const q = query(medicationsRef, where("userId", "==", user.uid));
      
      const unsubscribe = onSnapshot(q, 
        (snapshot) => {
          console.log("Jumlah dokumen obat:", snapshot.docs.length);
          const list = snapshot.docs.map((doc) => {
            console.log("Medication item:", doc.id, doc.data());
            return { id: doc.id, ...doc.data() };
          });
          // Urutkan berdasarkan tanggal dibuat (terbaru dulu)
          list.sort((a, b) => {
            const dateA = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
            const dateB = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
            return dateB - dateA;
          });
          setMedications(list);
        },
        (error) => {
          console.error("Error onSnapshot medications:", error);
          setError(error.message);
          
          // Fallback ke getDocs jika ada error
          if (error.code === 'failed-precondition') {
            console.log("Mencoba fallback ke getDocs...");
            getDocs(q).then((snapshot) => {
              const list = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
              list.sort((a, b) => {
                const dateA = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
                const dateB = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
                return dateB - dateA;
              });
              setMedications(list);
            }).catch(err => console.error("Fallback error:", err));
          }
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error("Error setting up medications listener:", error);
      setError(error.message);
    }
  }, [user]);

  // Fungsi untuk toggle status aktif/tidak aktif obat
  const toggleMedicationActive = async (id, currentStatus) => {
    try {
      await updateDoc(doc(db, "medications", id), { 
        isActive: !currentStatus,
        updatedAt: new Date()
      });
      console.log("Status obat berhasil diupdate");
    } catch (error) {
      console.error("Error toggle medication status:", error);
      Alert.alert("Error", "Gagal mengubah status obat");
    }
  };

  // Fungsi untuk menghapus obat
  const deleteMedication = async (id) => {
    console.log("=== DELETE FUNCTION CALLED ===");
    console.log("User:", user?.uid);
    console.log("Medication ID:", id);
    console.log("Database name: pencatatanobat");
    
    // Validasi user authentication
    if (!user) {
      console.error("User not authenticated");
      Alert.alert("Error", "User tidak terautentikasi");
      return;
    }

    // Validasi ID obat
    if (!id) {
      console.error("Invalid medication ID");
      Alert.alert("Error", "ID obat tidak valid");
      return;
    }

    // Cek apakah obat ada di list medications
    const medicationToDelete = medications.find(med => med.id === id);
    console.log("Medication to delete:", medicationToDelete);

    Alert.alert(
      "Konfirmasi Hapus",
      `Apakah Anda yakin ingin menghapus obat "${medicationToDelete?.name || 'Unknown'}"? Tindakan ini tidak dapat dibatalkan.`,
      [
        { 
          text: "Batal", 
          style: "cancel",
          onPress: () => console.log("Delete cancelled by user")
        },
        {
          text: "Hapus",
          style: "destructive",
          onPress: async () => {
            console.log("User confirmed delete, starting process...");
            setLoading(true);
            try {
              console.log("Attempting to delete from Firestore...");
              console.log("Collection: medications");
              console.log("Document ID:", id);
              
              // Hapus dokumen dari Firestore
              await deleteDoc(doc(db, "medications", id));
              
              console.log("✅ Document successfully deleted from Firestore");
              
              // Tampilkan notifikasi sukses
              Alert.alert(
                "Berhasil", 
                `Obat "${medicationToDelete?.name || 'Unknown'}" berhasil dihapus`,
                [{ text: "OK", onPress: () => console.log("Delete success acknowledged") }]
              );
              
            } catch (error) {
              console.error("❌ Error deleting medication:", error);
              console.error("Error code:", error.code);
              console.error("Error message:", error.message);
              
              // Error handling yang lebih detail
              let errorMessage = "Gagal menghapus obat";
              
              if (error.code === 'permission-denied') {
                errorMessage = "Anda tidak memiliki izin untuk menghapus obat ini. Periksa Firebase rules.";
              } else if (error.code === 'not-found') {
                errorMessage = "Obat tidak ditemukan di database atau sudah dihapus";
              } else if (error.code === 'unavailable') {
                errorMessage = "Layanan Firebase tidak tersedia. Periksa koneksi internet Anda";
              } else if (error.message) {
                errorMessage = `Gagal menghapus obat: ${error.message}`;
              }
              
              Alert.alert("Error", errorMessage);
            } finally {
              setLoading(false);
              console.log("Delete process completed");
            }
          }
        }
      ],
      { cancelable: true }
    );
  };

  // Fungsi untuk membuka modal edit
  const openEditModal = (medication) => {
    setEditMedication(medication);
    setEditModalVisible(true);
  };

  // Fungsi untuk menyimpan perubahan edit obat
  const handleEditSave = async (updatedMedicationData) => {
    if (!editMedication) {
      Alert.alert("Error", "Data obat tidak ditemukan");
      return;
    }

    setLoading(true);
    try {
      await updateDoc(doc(db, "medications", editMedication.id), updatedMedicationData);
      console.log("Obat berhasil diedit");
      Alert.alert("Sukses", "Obat berhasil diperbarui");
      setEditModalVisible(false);
      setEditMedication(null);
    } catch (error) {
      console.error("Error edit obat:", error);
      Alert.alert("Error", "Gagal mengedit obat: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk membatalkan edit
  const handleEditCancel = () => {
    setEditModalVisible(false);
    setEditMedication(null);
  };

  const handleLogout = async () => {
    console.log("=== LOGOUT INITIATED FROM MEDICATION LIST ===");
    try {
      await signOut(auth);
      console.log("Firebase signOut successful");
      // Redirect ke Login tanpa parameter reset
      router.replace("/Login");
    } catch (error) {
      console.error("Logout error:", error);
      Alert.alert("Error", "Gagal logout: " + error.message);
    }
  };

  // Tampilkan loading selama pengecekan user
  if (loadingUser) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Memuat...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Daftar Obat ({medications.length})</Text>
        <Text style={styles.welcomeText}>User: {user?.email}</Text>
      </View>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
          <Text style={styles.errorSubText}>
            Periksa console dan pastikan koneksi Firebase berfungsi dengan baik
          </Text>
        </View>
      )}

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>Memproses operasi...</Text>
        </View>
      )}

      {medications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Belum ada obat yang dicatat</Text>
          <Text style={styles.emptySubText}>Kembali ke halaman utama untuk menambah obat</Text>
          <Button 
            title="Tambah Obat" 
            onPress={() => router.back()} 
            color="#007AFF"
          />
        </View>
      ) : (
        <FlatList 
          data={medications} 
          keyExtractor={(item) => item.id} 
          renderItem={({ item }) => (
            <MedicationItem 
              item={item} 
              onToggleActive={toggleMedicationActive} 
              onDelete={deleteMedication} 
              onEdit={openEditModal} 
            />
          )}
          showsVerticalScrollIndicator={false}
          style={styles.list}
          contentContainerStyle={styles.listContent}
        />
      )}

      <View style={styles.buttonContainer}>
        <Button 
          title="Kembali ke Form" 
          onPress={() => router.back()} 
          color="#007AFF"
        />
        <View style={styles.buttonSpacer} />
        <Button title="Logout" color="red" onPress={handleLogout} />
      </View>

      <EditMedicationModal
        visible={editModalVisible}
        medication={editMedication}
        onSave={handleEditSave}
        onCancel={handleEditCancel}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  welcomeText: {
    fontSize: 14,
    color: "#666",
  },
  errorContainer: {
    margin: 20,
    padding: 15,
    backgroundColor: "#ffebee",
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#f44336",
  },
  errorText: {
    color: "#c62828",
    fontWeight: "600",
    marginBottom: 5,
  },
  errorSubText: {
    color: "#c62828",
    fontSize: 12,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  loadingText: {
    marginLeft: 10,
    color: "#1976d2",
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    color: "#666",
    textAlign: "center",
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
    marginBottom: 20,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    padding: 20,
    backgroundColor: "#ffffff",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  buttonSpacer: {
    width: 10,
  },
});
