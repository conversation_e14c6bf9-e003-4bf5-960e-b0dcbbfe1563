// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyA9vSr_l9F8yELrC6oe5cf4tQiYHZbDE80",
  authDomain: "pencatatanobat.firebaseapp.com",
  databaseURL: "https://pencatatanobat-default-rtdb.firebaseio.com",
  projectId: "pencatatanobat",
  storageBucket: "pencatatanobat.firebasestorage.app",
  messagingSenderId: "64608501672",
  appId: "1:64608501672:web:560976301ddf3623e4679d",
  measurementId: "G-84HFFMJRTN"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

const auth = getAuth(app );

const db = getFirestore(app);

export { auth, db };

