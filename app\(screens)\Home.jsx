import React, { useEffect, useState } from "react";
import { View, Text, Button, Alert, ActivityIndicator, StyleSheet, ScrollView, TouchableOpacity, TextInput } from "react-native";
import { useRouter } from "expo-router";
import { signOut, onAuthStateChanged } from "firebase/auth";
import { auth, db } from "../../config/firebase";
import { collection, addDoc, query, where, getDocs, onSnapshot, orderBy, limit } from "firebase/firestore";
import AddObatScreen from "./components/AddObatScreen"; // Contains AddObatScreen component

export default function Home() {
  const [user, setUser] = useState(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [medicationCount, setMedicationCount] = useState(0);
  const [latestMedication, setLatestMedication] = useState(null);
  const [addModalVisible, setAddModalVisible] = useState(false);
  // State untuk search
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const router = useRouter();

  // Cek user login
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (!currentUser) {
        router.replace("/Login");
      } else {
        setUser(currentUser);
        console.log("User ID saat ini:", currentUser.uid);
      }
      setLoadingUser(false);
    });

    return unsubscribe;
  }, []);

  // Fungsi untuk mendapatkan jumlah obat user dan obat terbaru
  const getMedicationData = async () => {
    if (!user) return;

    try {
      console.log("🔍 Fetching medication data for user:", user.uid);

      // Gunakan collection "obat" untuk konsistensi
      console.log(`🔍 Checking collection: obat`);
      const obatRef = collection(db, "obat");
      const q = query(obatRef, where("userId", "==", user.uid));
      const snapshot = await getDocs(q);

      console.log(`📊 Found ${snapshot.docs.length} documents in obat collection`);

      if (snapshot.docs.length > 0) {
        setMedicationCount(snapshot.docs.length);

        // Log semua data untuk debugging
        snapshot.docs.forEach((doc, index) => {
          console.log(`📄 Document ${index + 1}:`, doc.data());
        });

        // Ambil obat terbaru
        const latestQ = query(
          obatRef,
          where("userId", "==", user.uid),
          orderBy("createdAt", "desc"),
          limit(1)
        );
        const latestSnapshot = await getDocs(latestQ);
        if (latestSnapshot.docs.length > 0) {
          const latestData = latestSnapshot.docs[0].data();
          console.log("✅ Latest medication data:", latestData);
          setLatestMedication(latestData);
        }
      } else {
        console.log("❌ No medication data found in obat collection");
        setMedicationCount(0);
        setLatestMedication(null);
      }
    } catch (error) {
      console.error("❌ Error getting medication data:", error);
    }
  };

  // Update data saat user berubah
  useEffect(() => {
    if (user) {
      getMedicationData();
    }
  }, [user]);

  // Fungsi untuk search obat berdasarkan nama
  const searchMedications = async (searchQuery) => {
    if (!user || !searchQuery.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      console.log("🔍 Searching medications for query:", searchQuery);

      const obatRef = collection(db, "obat");
      const q = query(obatRef, where("userId", "==", user.uid));
      const snapshot = await getDocs(q);

      // Filter hasil berdasarkan nama obat (case insensitive)
      const filteredResults = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(medication =>
          medication.name &&
          medication.name.toLowerCase().includes(searchQuery.toLowerCase())
        );

      console.log("📊 Search results:", filteredResults.length);
      setSearchResults(filteredResults);
    } catch (error) {
      console.error("❌ Error searching medications:", error);
      Alert.alert("Error", "Gagal mencari obat: " + error.message);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle perubahan search query
  const handleSearchChange = (text) => {
    setSearchQuery(text);
    if (text.trim()) {
      searchMedications(text);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  };

  // Fungsi untuk menambah obat baru dengan auto-create table
  const addMedication = async (medicationData) => {
    if (!user) {
      Alert.alert("Error", "User tidak terautentikasi");
      return;
    }

    setLoading(true);
    try {
      console.log("Menambahkan obat baru untuk user:", user.uid);

      // Auto-create collection "obat" jika belum ada
      const obatRef = collection(db, "obat");

      const newMedication = {
        ...medicationData,
        userId: user.uid,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await addDoc(obatRef, newMedication);
      console.log("Obat berhasil ditambahkan dan tabel otomatis dibuat");
      Alert.alert("Sukses", "Obat berhasil ditambahkan");
      // Update data setelah menambah obat
      getMedicationData();
      // Tutup modal
      setAddModalVisible(false);
    } catch (error) {
      console.error("Error menambahkan obat:", error);
      Alert.alert("Error", "Gagal menambahkan obat: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk navigasi ke halaman detail obat
  const goToDetailObat = () => {
    router.push("/DetailObatScreen");
  };

  const handleLogout = async () => {
    console.log("=== LOGOUT INITIATED ===");
    try {
      await signOut(auth);
      console.log("Firebase signOut successful");
      // Redirect ke Login tanpa parameter reset
      router.replace("/Login");
    } catch (error) {
      console.error("Logout error:", error);
      Alert.alert("Error", "Gagal logout: " + error.message);
    }
  };

  // Tampilkan loading selama pengecekan user
  if (loadingUser) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header Biru */}
      <View style={styles.header}>
        <Text style={styles.title}>Pencatatan Obat</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Cari obat..."
            value={searchQuery}
            onChangeText={handleSearchChange}
            placeholderTextColor="#999"
          />
        </View>

        {/* Search Results */}
        {searchQuery.trim() !== "" && (
          <View style={styles.searchResultsContainer}>
            {isSearching ? (
              <View style={styles.searchLoadingContainer}>
                <ActivityIndicator size="small" color="#4A90E2" />
                <Text style={styles.searchLoadingText}>Mencari...</Text>
              </View>
            ) : searchResults.length > 0 ? (
              searchResults.map((medication) => (
                <TouchableOpacity
                  key={medication.id}
                  style={styles.searchResultCard}
                  onPress={() => {
                    // Navigate to detail or clear search
                    setSearchQuery("");
                    setSearchResults([]);
                    goToDetailObat();
                  }}
                >
                  <Text style={styles.searchResultName}>{medication.name}</Text>
                  <Text style={styles.searchResultDosis}>Dosis: {medication.dosage || 'Tidak ada'}</Text>
                  <Text style={styles.searchResultFrekuensi}>Frekuensi: {medication.frequency || 'Tidak ada'}</Text>
                  <Text style={styles.searchResultInstruksi}>Instruksi: {medication.instructions || 'Tidak ada'}</Text>
                  <Text style={styles.searchResultEfek}>Efek Samping: {medication.sideEffects || 'tidak ada'}</Text>
                  <Text style={styles.searchResultDate}>
                    Ditambahkan: {medication.createdAt?.toDate ?
                      medication.createdAt.toDate().toLocaleDateString('id-ID') :
                      'Tidak diketahui'
                    }
                  </Text>
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.noResultsContainer}>
                <Text style={styles.noResultsText}>Tidak ada obat yang ditemukan</Text>
              </View>
            )}
          </View>
        )}

        {/* Cards Container - Only show when not searching */}
        {searchQuery.trim() === "" && (
          <View style={styles.cardsContainer}>
            {/* Total Obat Card */}
            <TouchableOpacity style={styles.card} onPress={goToDetailObat}>
              <View style={styles.medicationIcon}>
                <Text style={styles.medicationEmoji}>💊</Text>
              </View>
              <Text style={styles.cardTitle}>Total Obat</Text>
              <Text style={styles.cardNumber}>{medicationCount}</Text>
            </TouchableOpacity>

            {/* Tambah Obat Card */}
            <TouchableOpacity style={styles.card} onPress={() => setAddModalVisible(true)}>
              <View style={styles.plusIcon}>
                <Text style={styles.plusText}>+</Text>
              </View>
              <Text style={styles.cardTitle}>Tambah Obat</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Box Obat Terakhir Ditambahkan - Temporarily removed for design */}

        {/* Error Display */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Error: {error}</Text>
          </View>
        )}

        {/* Loading Display */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#007AFF" />
            <Text style={styles.loadingText}>Memproses operasi...</Text>
          </View>
        )}
      </ScrollView>

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutText}>LOGOUT</Text>
        </TouchableOpacity>
      </View>

      {/* Add Medication Modal */}
      <AddObatScreen
        visible={addModalVisible}
        onAddMedication={addMedication}
        onCancel={() => setAddModalVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0f0f0",
  },
  header: {
    backgroundColor: "#4A90E2",
    paddingTop: 35,
    paddingBottom: 15,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#ffffff",
    textAlign: "center",
    letterSpacing: 0.5,
    marginTop: -20,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  latestMedicationContainer: {
    marginBottom: 20,
  },
  latestMedicationCard: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 15,
    marginTop: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  latestMedicationLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  latestMedicationName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  cardsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  card: {
    backgroundColor: "#ffffff",
    width: "48%",
    padding: 20,
    borderRadius: 15,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardIcon: {
    width: 40,
    height: 40,
    marginBottom: 10,
  },
  medicationIcon: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  medicationEmoji: {
    fontSize: 32,
  },
  plusIcon: {
    width: 50,
    height: 50,
    backgroundColor: "#333",
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 15,
  },
  plusText: {
    color: "#ffffff",
    fontSize: 28,
    fontWeight: "bold",
    marginTop: -6,
  },
  cardTitle: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginBottom: 5,
  },
  cardNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#4A90E2",
  },
  errorContainer: {
    margin: 10,
    padding: 15,
    backgroundColor: "#ffebee",
    borderRadius: 8,
  },
  errorText: {
    color: "#c62828",
    fontWeight: "600",
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    backgroundColor: "#e3f2fd",
    borderRadius: 8,
    margin: 10,
  },
  loadingText: {
    marginLeft: 10,
    color: "#1976d2",
    fontSize: 14,
  },
  logoutContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  logoutButton: {
    backgroundColor: "#E74C3C",
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: "center",
  },
  logoutText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "bold",
  },
  // Styles untuk box obat terakhir di bawah
  latestMedicationBottomContainer: {
    marginTop: 10,
    marginBottom: 20,
  },
  latestMedicationBottomLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  latestMedicationBottomCard: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 15,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  latestMedicationBottomName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  // Styles untuk search
  searchContainer: {
    marginBottom: 20,
  },
  searchInput: {
    backgroundColor: "#ffffff",
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchResultsContainer: {
    marginBottom: 20,
  },
  searchLoadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
    backgroundColor: "#f8f9fa",
    borderRadius: 10,
  },
  searchLoadingText: {
    marginLeft: 10,
    color: "#4A90E2",
    fontSize: 14,
  },
  searchResultCard: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 15,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  searchResultName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  searchResultDosis: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  searchResultFrekuensi: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  searchResultInstruksi: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  searchResultEfek: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  searchResultDate: {
    fontSize: 12,
    color: "#999",
    fontStyle: "italic",
    marginTop: 8,
  },
  noResultsContainer: {
    padding: 20,
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    borderRadius: 10,
  },
  noResultsText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});