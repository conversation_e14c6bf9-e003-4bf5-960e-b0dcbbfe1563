import { useLocalSearchParams, useRouter } from "expo-router";
import { onAuthStateChanged, signInWithEmailAndPassword } from "firebase/auth";
import { useEffect, useState } from "react";
import { ActivityIndicator, Button, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import { auth } from "../../config/firebase";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [inputsDisabled, setInputsDisabled] = useState(false);
  const router = useRouter();
  const params = useLocalSearchParams();

  // Debug logging
  console.log("=== LOGIN COMPONENT RENDERED ===");
  console.log("Email state:", email);
  console.log("Password state:", password);
  console.log("Params:", params);
  console.log("Inputs disabled:", inputsDisabled);

  // Reset input fields jika kembali dari halaman lain dengan reset=true
  useEffect(() => {
    console.log("Reset useEffect triggered, params.reset:", params.reset);
    if (params.reset === "true") {
      console.log("Resetting email and password fields");
      setEmail("");
      setPassword("");
      // Reset params setelah reset untuk mencegah loop
      setTimeout(() => {
        router.replace("/Login");
      }, 100);
    }
  }, [params]);

  // Cek apakah user sudah login
  useEffect(() => {
    console.log("Auth state change listener setup");
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log("Auth state changed, user:", user?.email || "No user");
      if (user) {
        // User sudah login, redirect ke Home
        console.log("User authenticated, redirecting to Home");
        router.replace("/Home");
      } else {
        console.log("No user authenticated");
        setInputsDisabled(false); // Enable inputs when no user
      }
    });

    return unsubscribe;
  }, []);

  const handleLogin = async () => {
    console.log("=== LOGIN ATTEMPT ===");
    console.log("Email:", email);
    console.log("Password length:", password.length);

    if (email.trim() === "" || password.trim() === "") {
      setError("Email dan password tidak boleh kosong");
      return;
    }

    setLoading(true);
    setError("");
    setInputsDisabled(true);

    try {
      console.log("Attempting Firebase login...");
      await signInWithEmailAndPassword(auth, email, password);
      console.log("Login successful, redirecting...");
      router.replace("/Home");
    } catch (error) {
      console.error("Login error:", error);

      if (error.code === "auth/invalid-credential") {
        setError("Email atau password salah");
      } else {
        setError("Terjadi kesalahan saat login. Silakan coba lagi.");
      }
    } finally {
      setLoading(false);
      setInputsDisabled(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Login</Text>

      {error ? <Text style={styles.errorText}>{error}</Text> : null}

      <TextInput
        style={[styles.input, inputsDisabled && styles.disabledInput]}
        placeholder="Email"
        placeholderTextColor="#999999"
        value={email}
        onChangeText={(text) => {
          console.log("Email input changed:", text);
          setEmail(text);
        }}
        keyboardType="email-address"
        autoCapitalize="none"
        editable={!inputsDisabled}
        selectTextOnFocus={!inputsDisabled}
      />

      <TextInput
        style={[styles.input, inputsDisabled && styles.disabledInput]}
        placeholder="Password"
        placeholderTextColor="#999999"
        value={password}
        onChangeText={(text) => {
          console.log("Password input changed, length:", text.length);
          setPassword(text);
        }}
        secureTextEntry
        editable={!inputsDisabled}
        selectTextOnFocus={!inputsDisabled}
      />

      {loading ? (
        <ActivityIndicator size="large" style={styles.button} />
      ) : (
        <Button title="Login" onPress={handleLogin} />
      )}

      <View style={styles.signupContainer}>
        <Text>Belum punya akun? </Text>
        <TouchableOpacity onPress={() => router.push("/Signup")}>
          <Text style={styles.signupText}>Daftar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    padding: 10,
    marginBottom: 15,
    borderRadius: 5,
    backgroundColor: "#ffffff",
    fontSize: 16,
  },
  disabledInput: {
    backgroundColor: "#f5f5f5",
    borderColor: "#ddd",
    color: "#999",
  },
  button: {
    marginTop: 10,
  },
  errorText: {
    color: "red",
    marginBottom: 15,
  },
  signupContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  signupText: {
    color: "blue",
    fontWeight: "bold",
  },
}); 