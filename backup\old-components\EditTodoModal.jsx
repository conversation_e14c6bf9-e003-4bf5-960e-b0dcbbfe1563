import React, { useState, useEffect } from "react";
import { Modal, View, Text, TextInput, Button, StyleSheet, ScrollView, Alert } from "react-native";

export default function EditMedicationModal({ visible, medication, onSave, onCancel }) {
  const [medicationName, setMedicationName] = useState("");
  const [dosage, setDosage] = useState("");
  const [frequency, setFrequency] = useState("");
  const [instructions, setInstructions] = useState("");
  const [sideEffects, setSideEffects] = useState("");

  useEffect(() => {
    if (medication) {
      setMedicationName(medication.name || "");
      setDosage(medication.dosage || "");
      setFrequency(medication.frequency || "");
      setInstructions(medication.instructions || "");
      setSideEffects(medication.sideEffects || "");
    }
  }, [medication]);

  const handleSave = () => {
    // Validasi input
    if (!medicationName.trim()) {
      Alert.alert("Error", "Nama obat harus diisi");
      return;
    }
    if (!dosage.trim()) {
      Alert.alert("Error", "Dosis obat harus diisi");
      return;
    }
    if (!frequency.trim()) {
      Alert.alert("Error", "Frekuensi konsumsi harus diisi");
      return;
    }

    const updatedMedication = {
      name: medicationName.trim(),
      dosage: dosage.trim(),
      frequency: frequency.trim(),
      instructions: instructions.trim(),
      sideEffects: sideEffects.trim(),
      updatedAt: new Date(),
    };

    onSave(updatedMedication);
  };

  const handleCancel = () => {
    // Reset form
    setMedicationName("");
    setDosage("");
    setFrequency("");
    setInstructions("");
    setSideEffects("");
    onCancel();
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={handleCancel}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ScrollView style={styles.scrollView}>
            <Text style={styles.title}>Edit Obat</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Nama Obat *</Text>
              <TextInput
                value={medicationName}
                onChangeText={setMedicationName}
                style={styles.input}
                placeholder="Nama obat"
                placeholderTextColor="#999999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Dosis *</Text>
              <TextInput
                value={dosage}
                onChangeText={setDosage}
                style={styles.input}
                placeholder="Dosis obat"
                placeholderTextColor="#999999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Frekuensi Konsumsi *</Text>
              <TextInput
                value={frequency}
                onChangeText={setFrequency}
                style={styles.input}
                placeholder="Frekuensi konsumsi"
                placeholderTextColor="#999999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Instruksi Penggunaan</Text>
              <TextInput
                value={instructions}
                onChangeText={setInstructions}
                style={[styles.input, styles.textArea]}
                placeholder="Instruksi penggunaan"
                placeholderTextColor="#999999"
                multiline={true}
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Efek Samping</Text>
              <TextInput
                value={sideEffects}
                onChangeText={setSideEffects}
                style={[styles.input, styles.textArea]}
                placeholder="Efek samping"
                placeholderTextColor="#999999"
                multiline={true}
                numberOfLines={3}
              />
            </View>

            <View style={styles.buttonContainer}>
              <View style={styles.button}>
                <Button title="Simpan" onPress={handleSave} />
              </View>
              <View style={styles.button}>
                <Button title="Batal" color="gray" onPress={handleCancel} />
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "#000000aa",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: "white",
    padding: 20,
    width: "90%",
    maxHeight: "80%",
    borderRadius: 15,
  },
  scrollView: {
    maxHeight: "100%",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
    color: "#333",
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 5,
    color: "#333",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: "top",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});