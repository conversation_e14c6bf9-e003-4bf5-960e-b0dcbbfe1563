import React, { useState } from "react";
import { TextInput, Button, View, Text, StyleSheet, ScrollView, Alert } from "react-native";

export default function MedicationInput({ onAddMedication }) {
  const [medicationName, setMedicationName] = useState("");
  const [dosage, setDosage] = useState("");
  const [frequency, setFrequency] = useState("");
  const [instructions, setInstructions] = useState("");
  const [sideEffects, setSideEffects] = useState("");

  const handleAddMedication = () => {
    // Validasi input
    if (!medicationName.trim()) {
      Alert.alert("Error", "Nama obat harus diisi");
      return;
    }
    if (!dosage.trim()) {
      Alert.alert("Error", "Dosis obat harus diisi");
      return;
    }
    if (!frequency.trim()) {
      Alert.alert("Error", "Frekuensi konsumsi harus diisi");
      return;
    }

    const medicationData = {
      name: medicationName.trim(),
      dosage: dosage.trim(),
      frequency: frequency.trim(),
      instructions: instructions.trim(),
      sideEffects: sideEffects.trim(),
      isActive: true,
      createdAt: new Date(),
    };

    onAddMedication(medicationData);

    // Reset form
    setMedicationName("");
    setDosage("");
    setFrequency("");
    setInstructions("");
    setSideEffects("");
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Tambah Obat Baru</Text>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Nama Obat *</Text>
        <TextInput
          placeholder="Contoh: Paracetamol"
          placeholderTextColor="#999999"
          value={medicationName}
          onChangeText={setMedicationName}
          style={styles.input}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Dosis *</Text>
        <TextInput
          placeholder="Contoh: 500mg"
          placeholderTextColor="#999999"
          value={dosage}
          onChangeText={setDosage}
          style={styles.input}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Frekuensi Konsumsi *</Text>
        <TextInput
          placeholder="Contoh: 3x sehari"
          placeholderTextColor="#999999"
          value={frequency}
          onChangeText={setFrequency}
          style={styles.input}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Instruksi Penggunaan</Text>
        <TextInput
          placeholder="Contoh: Diminum setelah makan"
          placeholderTextColor="#999999"
          value={instructions}
          onChangeText={setInstructions}
          style={[styles.input, styles.textArea]}
          multiline={true}
          numberOfLines={3}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Efek Samping</Text>
        <TextInput
          placeholder="Contoh: Mengantuk, mual"
          placeholderTextColor="#999999"
          value={sideEffects}
          onChangeText={setSideEffects}
          style={[styles.input, styles.textArea]}
          multiline={true}
          numberOfLines={3}
        />
      </View>

      <Button title="Tambah Obat" onPress={handleAddMedication} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
    textAlign: "center",
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 5,
    color: "#333",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: "top",
  },
});