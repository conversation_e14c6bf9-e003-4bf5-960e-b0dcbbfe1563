import React, { useState } from "react";
import { TextInput, Button, View, Text, StyleSheet, ScrollView, Alert, Modal } from "react-native";

export default function AddObatScreen({ visible, onAddMedication, onCancel }) {
  const [medicationName, setMedicationName] = useState("");
  const [dosage, setDosage] = useState("");
  const [frequency, setFrequency] = useState("");
  const [instructions, setInstructions] = useState("");
  const [sideEffects, setSideEffects] = useState("");

  const handleAddMedication = () => {
    // Validasi input
    if (!medicationName.trim()) {
      Alert.alert("Error", "Nama obat harus diisi");
      return;
    }
    if (!dosage.trim()) {
      Alert.alert("Error", "Dosis obat harus diisi");
      return;
    }
    if (!frequency.trim()) {
      Alert.alert("Error", "Frekuensi konsumsi harus diisi");
      return;
    }

    const medicationData = {
      name: medicationName.trim(),
      dosage: dosage.trim(),
      frequency: frequency.trim(),
      instructions: instructions.trim(),
      sideEffects: sideEffects.trim(),
      isActive: true,
      createdAt: new Date(),
    };

    onAddMedication(medicationData);

    // Reset form
    setMedicationName("");
    setDosage("");
    setFrequency("");
    setInstructions("");
    setSideEffects("");
  };

  const handleCancel = () => {
    // Reset form
    setMedicationName("");
    setDosage("");
    setFrequency("");
    setInstructions("");
    setSideEffects("");
    onCancel();
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={handleCancel}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ScrollView style={styles.scrollView}>
            <Text style={styles.title}>Tambah Obat</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Nama Obat *</Text>
              <TextInput
                placeholder="Contoh: Paracetamol"
                placeholderTextColor="#999999"
                value={medicationName}
                onChangeText={setMedicationName}
                style={styles.input}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Dosis *</Text>
              <TextInput
                placeholder="Contoh: 500mg"
                placeholderTextColor="#999999"
                value={dosage}
                onChangeText={setDosage}
                style={styles.input}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Frekuensi Konsumsi *</Text>
              <TextInput
                placeholder="Contoh: 3x sehari"
                placeholderTextColor="#999999"
                value={frequency}
                onChangeText={setFrequency}
                style={styles.input}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Instruksi Penggunaan</Text>
              <TextInput
                placeholder="Contoh: Diminum setelah makan"
                placeholderTextColor="#999999"
                value={instructions}
                onChangeText={setInstructions}
                style={[styles.input, styles.textArea]}
                multiline={true}
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Efek Samping</Text>
              <TextInput
                placeholder="Contoh: Mengantuk, mual"
                placeholderTextColor="#999999"
                value={sideEffects}
                onChangeText={setSideEffects}
                style={[styles.input, styles.textArea]}
                multiline={true}
                numberOfLines={3}
              />
            </View>

            <View style={styles.buttonContainer}>
              <View style={styles.button}>
                <Button title="TAMBAH" onPress={handleAddMedication} />
              </View>
              <View style={styles.button}>
                <Button title="BATAL" color="gray" onPress={handleCancel} />
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "#000000aa",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: "white",
    padding: 20,
    width: "90%",
    maxHeight: "80%",
    borderRadius: 15,
  },
  scrollView: {
    maxHeight: "100%",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
    color: "#333",
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 5,
    color: "#333",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: "top",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});
