import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

export default function MedicationItem({ item, onToggleActive, onDelete, onEdit }) {
  const formatDate = (date) => {
    if (!date) return "";
    const d = date.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString("id-ID");
  };

  return (
    <View style={[styles.item, !item.isActive && styles.inactiveItem]}>
      <TouchableOpacity onPress={() => onToggleActive(item.id, item.isActive)} style={styles.contentContainer}>
        <View style={styles.header}>
          <Text style={[styles.medicationName, !item.isActive && styles.inactiveText]}>
            {item.name}
          </Text>
          <Text style={[styles.status, item.isActive ? styles.activeStatus : styles.inactiveStatus]}>
            {item.isActive ? "Aktif" : "Tidak Aktif"}
          </Text>
        </View>
        
        <View style={styles.details}>
          <Text style={[styles.detailText, !item.isActive && styles.inactiveText]}>
            <Text style={styles.label}>Dosis: </Text>{item.dosage}
          </Text>
          <Text style={[styles.detailText, !item.isActive && styles.inactiveText]}>
            <Text style={styles.label}>Frekuensi: </Text>{item.frequency}
          </Text>
          {item.instructions && (
            <Text style={[styles.detailText, !item.isActive && styles.inactiveText]}>
              <Text style={styles.label}>Instruksi: </Text>{item.instructions}
            </Text>
          )}
          {item.sideEffects && (
            <Text style={[styles.detailText, !item.isActive && styles.inactiveText]}>
              <Text style={styles.label}>Efek Samping: </Text>{item.sideEffects}
            </Text>
          )}
          <Text style={[styles.dateText, !item.isActive && styles.inactiveText]}>
            Ditambahkan: {formatDate(item.createdAt)}
          </Text>
        </View>
      </TouchableOpacity>
      
      <View style={styles.actions}>
        <TouchableOpacity onPress={() => onEdit(item)} style={styles.actionButton}>
          <Text style={styles.actionText}>✏️</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => onDelete(item.id)} style={styles.actionButton}>
          <Text style={styles.actionText}>🗑️</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  item: {
    paddingVertical: 15,
    paddingHorizontal: 16,
    backgroundColor: "#ffffff",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    marginBottom: 8,
    borderRadius: 8,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  inactiveItem: {
    backgroundColor: "#f5f5f5",
    opacity: 0.7,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  medicationName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    flex: 1,
  },
  status: {
    fontSize: 12,
    fontWeight: "600",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: "hidden",
  },
  activeStatus: {
    backgroundColor: "#e8f5e8",
    color: "#2e7d32",
  },
  inactiveStatus: {
    backgroundColor: "#ffebee",
    color: "#c62828",
  },
  details: {
    marginTop: 5,
  },
  detailText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 3,
  },
  label: {
    fontWeight: "600",
    color: "#333",
  },
  dateText: {
    fontSize: 12,
    color: "#999",
    marginTop: 5,
    fontStyle: "italic",
  },
  inactiveText: {
    color: "#999",
  },
  actions: {
    flexDirection: "row",
    marginLeft: 10,
    alignItems: "flex-start",
  },
  actionButton: {
    padding: 8,
  },
  actionText: {
    fontSize: 18,
    marginLeft: 5,
  },
});
